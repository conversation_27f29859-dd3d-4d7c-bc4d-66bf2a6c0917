# NiceShop E-commerce Marketplace

A complete e-commerce marketplace application built with React, Node.js, and modern web technologies. This application replicates the NiceShop Bootstrap template with full functionality including customer and seller workflows, order management, coupon system, and email notifications.

## 🚀 Features

### Customer Features
- **Product Browsing**: Browse products by category, search, and filter
- **Shopping Cart**: Add products to cart with quantity management
- **Checkout Process**: Complete checkout with coupon code support
- **Order Tracking**: View order history and track shipments via Bluedart
- **User Authentication**: Secure login and registration

### Seller Features
- **Seller Dashboard**: Overview of products, orders, and revenue
- **Product Management**: Add new products with images and inventory
- **Order Management**: Update order status and generate tracking numbers
- **Coupon System**: Create and manage discount coupons
- **Inventory Tracking**: Real-time inventory management

### System Features
- **Dark/Light Theme**: Toggle between themes with persistent preference
- **Email Notifications**: Order confirmation and shipping notifications
- **Responsive Design**: Mobile-first responsive design
- **Mock Data**: In-memory database simulation for demo purposes

## 🛠️ Tech Stack

### Frontend
- **React 18** with Vite
- **Tailwind CSS** for styling
- **Shadcn UI** components
- **Framer Motion** for animations
- **Lucide React** for icons
- **Axios** for API calls

### Backend
- **Node.js** with Express
- **JWT** for authentication
- **bcryptjs** for password hashing
- **Nodemailer** for email notifications
- **UUID** for unique identifiers
- **In-memory data storage** (simulating database)

## 📦 Installation & Setup

### Prerequisites
- Node.js (v16 or higher)
- npm or yarn

### 1. Clone the Repository
```bash
git clone <repository-url>
cd ShivKrupa_Ecommerce
```

### 2. Install Dependencies

#### Backend
```bash
cd server
npm install
```

#### Frontend
```bash
cd client
npm install
```

### 3. Environment Configuration

Create a `.env` file in the `server` directory:
```env
PORT=5000
JWT_SECRET=niceshop-secret-key-2024-super-secure
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password
```

### 4. Start the Application

#### Start Backend Server
```bash
cd server
npm start
```
The server will run on `http://localhost:5000`

#### Start Frontend Development Server
```bash
cd client
npm run dev
```
The client will run on `http://localhost:3000` (or next available port)

## 🎯 Demo Credentials

### Customer Account
- **Email**: <EMAIL>
- **Password**: password123

### Seller Account
- **Email**: <EMAIL>
- **Password**: password123

### Demo Coupon Code
- **Code**: WELCOME10
- **Discount**: 10% off

## 🔧 API Endpoints

### Authentication
- `POST /api/auth/register` - User registration
- `POST /api/auth/login` - User login

### Products
- `GET /api/products` - Get all products (with filtering)
- `GET /api/products/:id` - Get single product
- `POST /api/products` - Create product (seller only)
- `GET /api/seller/products` - Get seller's products

### Orders
- `GET /api/orders` - Get user orders
- `POST /api/orders` - Create new order
- `PUT /api/orders/:id/status` - Update order status (seller only)

### Coupons
- `GET /api/coupons` - Get seller's coupons
- `POST /api/coupons` - Create coupon (seller only)
- `POST /api/coupons/validate` - Validate coupon code

### Categories
- `GET /api/categories` - Get all categories

## 🎨 Design & UI

The application follows the NiceShop Bootstrap template design with:
- **Modern gradient hero section**
- **Clean product cards with hover effects**
- **Professional dashboard layouts**
- **Responsive navigation**
- **Dark/Light theme support**

## 📱 Responsive Design

The application is fully responsive and works on:
- Desktop (1200px+)
- Tablet (768px - 1199px)
- Mobile (320px - 767px)

## 🔒 Security Features

- JWT token authentication
- Password hashing with bcrypt
- Protected routes for sellers and customers
- Input validation and sanitization
- CORS enabled for cross-origin requests

## 📧 Email Notifications

The system sends email notifications for:
- Order confirmation when order is placed
- Shipping notification with tracking details
- Uses Nodemailer with Gmail SMTP (configurable)

## 🚚 Order Tracking

- Integration with mock Bluedart tracking system
- Automatic tracking number generation
- Clickable tracking links in emails and order history

## 🎫 Coupon System

- Percentage-based discount coupons
- Expiry date validation
- Seller-specific coupon creation
- Real-time coupon validation during checkout

## 🗄️ Data Structure

The application uses in-memory arrays to simulate database tables:
- `users` - User accounts and authentication
- `products` - Product catalog with inventory
- `orders` - Order history and status
- `coupons` - Discount coupons

## 🔄 Development Workflow

1. **Backend Development**: Express server with REST API
2. **Frontend Development**: React components with modern hooks
3. **State Management**: Context API for global state
4. **Styling**: Tailwind CSS with custom components
5. **Testing**: Manual testing with demo data

## 🚀 Deployment

For production deployment:
1. Build the React app: `npm run build` in client directory
2. Serve static files from Express server
3. Configure environment variables
4. Set up proper email service (Gmail, SendGrid, etc.)
5. Replace in-memory storage with actual database

## 📝 License

This project is for demonstration purposes. Built as a proof of concept for modern e-commerce applications.

## 🤝 Contributing

This is a demo project. For improvements or suggestions, please create an issue or pull request.

---

**Built with ❤️ for modern e-commerce excellence**
