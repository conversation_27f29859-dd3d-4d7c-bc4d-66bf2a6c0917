import express from 'express';
import cors from 'cors';
import jwt from 'jsonwebtoken';
import bcrypt from 'bcryptjs';
import nodemailer from 'nodemailer';
import { v4 as uuidv4 } from 'uuid';
import dotenv from 'dotenv';

dotenv.config();

const app = express();
const PORT = process.env.PORT || 5000;
const JWT_SECRET = process.env.JWT_SECRET || 'niceshop-secret-key-2024';

// Middleware
app.use(cors());
app.use(express.json());

// In-memory data structures (simulating database)
let users = [
  {
    id: uuidv4(),
    name: '<PERSON>er',
    email: '<EMAIL>',
    password: bcrypt.hashSync('password123', 10),
    role: 'customer'
  },
  {
    id: uuidv4(),
    name: '<PERSON>',
    email: '<EMAIL>',
    password: bcrypt.hashSync('password123', 10),
    role: 'seller'
  }
];

let products = [
  {
    id: uuidv4(),
    sellerId: users[1].id,
    name: 'Premium Wireless Headphones',
    description: 'High-quality wireless headphones with noise cancellation',
    category: 'Electronics',
    price: 299.99,
    inventory: 50,
    images: ['https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=500']
  },
  {
    id: uuidv4(),
    sellerId: users[1].id,
    name: 'Smart Watch Pro',
    description: 'Advanced smartwatch with health monitoring features',
    category: 'Electronics',
    price: 399.99,
    inventory: 30,
    images: ['https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=500']
  },
  {
    id: uuidv4(),
    sellerId: users[1].id,
    name: 'Organic Cotton T-Shirt',
    description: 'Comfortable organic cotton t-shirt in various colors',
    category: 'Fashion',
    price: 29.99,
    inventory: 100,
    images: ['https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=500']
  },
  {
    id: uuidv4(),
    sellerId: users[1].id,
    name: 'Professional Camera',
    description: 'High-resolution DSLR camera for professional photography',
    category: 'Electronics',
    price: 1299.99,
    inventory: 15,
    images: ['https://images.unsplash.com/photo-1502920917128-1aa500764cbd?w=500']
  }
];

let orders = [];
let coupons = [
  {
    id: uuidv4(),
    sellerId: users[1].id,
    code: 'WELCOME10',
    discountValue: 10,
    validUntil: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
    productIds: [] // Empty means applies to all products
  }
];

// Email configuration
const transporter = nodemailer.createTransport({
  host: 'smtp.gmail.com',
  port: 587,
  secure: false,
  auth: {
    user: process.env.EMAIL_USER || '<EMAIL>',
    pass: process.env.EMAIL_PASS || 'testpassword'
  }
});

// Middleware to verify JWT token
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({ message: 'Access token required' });
  }

  jwt.verify(token, JWT_SECRET, (err, user) => {
    if (err) {
      return res.status(403).json({ message: 'Invalid or expired token' });
    }
    req.user = user;
    next();
  });
};

// Middleware to check seller role
const requireSeller = (req, res, next) => {
  if (req.user.role !== 'seller') {
    return res.status(403).json({ message: 'Seller access required' });
  }
  next();
};

// Auth Routes
app.post('/api/auth/register', async (req, res) => {
  try {
    const { name, email, password, role } = req.body;
    
    // Check if user already exists
    const existingUser = users.find(u => u.email === email);
    if (existingUser) {
      return res.status(400).json({ message: 'User already exists' });
    }

    // Create new user
    const hashedPassword = bcrypt.hashSync(password, 10);
    const newUser = {
      id: uuidv4(),
      name,
      email,
      password: hashedPassword,
      role: role || 'customer'
    };

    users.push(newUser);

    // Generate JWT token
    const token = jwt.sign(
      { id: newUser.id, email: newUser.email, role: newUser.role },
      JWT_SECRET,
      { expiresIn: '24h' }
    );

    res.status(201).json({
      message: 'User registered successfully',
      token,
      user: { id: newUser.id, name: newUser.name, email: newUser.email, role: newUser.role }
    });
  } catch (error) {
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

app.post('/api/auth/login', async (req, res) => {
  try {
    const { email, password } = req.body;

    // Find user
    const user = users.find(u => u.email === email);
    if (!user) {
      return res.status(400).json({ message: 'Invalid credentials' });
    }

    // Check password
    const isValidPassword = bcrypt.compareSync(password, user.password);
    if (!isValidPassword) {
      return res.status(400).json({ message: 'Invalid credentials' });
    }

    // Generate JWT token
    const token = jwt.sign(
      { id: user.id, email: user.email, role: user.role },
      JWT_SECRET,
      { expiresIn: '24h' }
    );

    res.json({
      message: 'Login successful',
      token,
      user: { id: user.id, name: user.name, email: user.email, role: user.role }
    });
  } catch (error) {
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// Product Routes
app.get('/api/products', (req, res) => {
  const { category, search } = req.query;
  let filteredProducts = [...products];

  if (category && category !== 'all') {
    filteredProducts = filteredProducts.filter(p => 
      p.category.toLowerCase() === category.toLowerCase()
    );
  }

  if (search) {
    filteredProducts = filteredProducts.filter(p =>
      p.name.toLowerCase().includes(search.toLowerCase()) ||
      p.description.toLowerCase().includes(search.toLowerCase())
    );
  }

  res.json(filteredProducts);
});

app.get('/api/products/:id', (req, res) => {
  const product = products.find(p => p.id === req.params.id);
  if (!product) {
    return res.status(404).json({ message: 'Product not found' });
  }
  res.json(product);
});

app.post('/api/products', authenticateToken, requireSeller, (req, res) => {
  try {
    const { name, description, category, price, inventory, images } = req.body;

    const newProduct = {
      id: uuidv4(),
      sellerId: req.user.id,
      name,
      description,
      category,
      price: parseFloat(price),
      inventory: parseInt(inventory),
      images: images || []
    };

    products.push(newProduct);
    res.status(201).json({ message: 'Product created successfully', product: newProduct });
  } catch (error) {
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// Get seller's products
app.get('/api/seller/products', authenticateToken, requireSeller, (req, res) => {
  const sellerProducts = products.filter(p => p.sellerId === req.user.id);
  res.json(sellerProducts);
});

// Order Routes
app.post('/api/orders', authenticateToken, async (req, res) => {
  try {
    const { items, couponCode } = req.body;
    let totalAmount = 0;
    let discount = 0;

    // Calculate total and check inventory
    for (const item of items) {
      const product = products.find(p => p.id === item.productId);
      if (!product) {
        return res.status(400).json({ message: `Product ${item.productId} not found` });
      }
      if (product.inventory < item.quantity) {
        return res.status(400).json({ message: `Insufficient inventory for ${product.name}` });
      }
      totalAmount += product.price * item.quantity;
    }

    // Apply coupon if provided
    if (couponCode) {
      const coupon = coupons.find(c => c.code === couponCode && new Date() <= new Date(c.validUntil));
      if (coupon) {
        discount = (totalAmount * coupon.discountValue) / 100;
      }
    }

    const finalAmount = totalAmount - discount;

    // Create order
    const newOrder = {
      id: uuidv4(),
      customerId: req.user.id,
      products: items,
      totalAmount: finalAmount,
      discount,
      status: 'Placed',
      trackingNumber: null,
      shippingPartner: 'Bluedart',
      createdAt: new Date(),
      updatedAt: new Date()
    };

    orders.push(newOrder);

    // Update inventory
    for (const item of items) {
      const product = products.find(p => p.id === item.productId);
      product.inventory -= item.quantity;
    }

    // Send order confirmation email
    try {
      const user = users.find(u => u.id === req.user.id);
      await transporter.sendMail({
        from: process.env.EMAIL_USER || '<EMAIL>',
        to: user.email,
        subject: 'Order Confirmation - NiceShop',
        html: `
          <h2>Order Confirmation</h2>
          <p>Dear ${user.name},</p>
          <p>Your order #${newOrder.id} has been placed successfully.</p>
          <p>Total Amount: $${finalAmount.toFixed(2)}</p>
          <p>Status: ${newOrder.status}</p>
          <p>Thank you for shopping with NiceShop!</p>
        `
      });
    } catch (emailError) {
      console.log('Email sending failed:', emailError.message);
    }

    res.status(201).json({ message: 'Order placed successfully', order: newOrder });
  } catch (error) {
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

app.get('/api/orders', authenticateToken, (req, res) => {
  let userOrders;
  if (req.user.role === 'customer') {
    userOrders = orders.filter(o => o.customerId === req.user.id);
  } else if (req.user.role === 'seller') {
    // Get orders containing seller's products
    userOrders = orders.filter(order => 
      order.products.some(item => {
        const product = products.find(p => p.id === item.productId);
        return product && product.sellerId === req.user.id;
      })
    );
  }
  res.json(userOrders);
});

app.put('/api/orders/:id/status', authenticateToken, requireSeller, async (req, res) => {
  try {
    const { status } = req.body;
    const order = orders.find(o => o.id === req.params.id);
    
    if (!order) {
      return res.status(404).json({ message: 'Order not found' });
    }

    order.status = status;
    order.updatedAt = new Date();

    // Generate tracking number when shipped
    if (status === 'Shipped' && !order.trackingNumber) {
      order.trackingNumber = `BD${Date.now()}${Math.random().toString(36).substr(2, 5).toUpperCase()}`;
    }

    // Send shipping notification email
    if (status === 'Shipped') {
      try {
        const customer = users.find(u => u.id === order.customerId);
        await transporter.sendMail({
          from: process.env.EMAIL_USER || '<EMAIL>',
          to: customer.email,
          subject: 'Order Shipped - NiceShop',
          html: `
            <h2>Order Shipped</h2>
            <p>Dear ${customer.name},</p>
            <p>Your order #${order.id} has been shipped!</p>
            <p>Tracking Number: ${order.trackingNumber}</p>
            <p>Shipping Partner: ${order.shippingPartner}</p>
            <p><a href="https://mock.bluedart-tracking.com/?id=${order.trackingNumber}">Track Your Order</a></p>
            <p>Thank you for shopping with NiceShop!</p>
          `
        });
      } catch (emailError) {
        console.log('Email sending failed:', emailError.message);
      }
    }

    res.json({ message: 'Order status updated successfully', order });
  } catch (error) {
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// Coupon Routes
app.get('/api/coupons', authenticateToken, requireSeller, (req, res) => {
  const sellerCoupons = coupons.filter(c => c.sellerId === req.user.id);
  res.json(sellerCoupons);
});

app.post('/api/coupons', authenticateToken, requireSeller, (req, res) => {
  try {
    const { code, discountValue, validUntil, productIds } = req.body;

    const newCoupon = {
      id: uuidv4(),
      sellerId: req.user.id,
      code: code.toUpperCase(),
      discountValue: parseFloat(discountValue),
      validUntil: new Date(validUntil),
      productIds: productIds || []
    };

    coupons.push(newCoupon);
    res.status(201).json({ message: 'Coupon created successfully', coupon: newCoupon });
  } catch (error) {
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

app.post('/api/coupons/validate', (req, res) => {
  const { code } = req.body;
  const coupon = coupons.find(c => c.code === code.toUpperCase() && new Date() <= new Date(c.validUntil));
  
  if (coupon) {
    res.json({ valid: true, discount: coupon.discountValue });
  } else {
    res.json({ valid: false, message: 'Invalid or expired coupon' });
  }
});

// Categories endpoint
app.get('/api/categories', (req, res) => {
  const categories = [...new Set(products.map(p => p.category))];
  res.json(categories);
});

// Health check
app.get('/api/health', (req, res) => {
  res.json({ status: 'OK', message: 'NiceShop API is running' });
});

app.listen(PORT, () => {
  console.log(`🚀 NiceShop server running on port ${PORT}`);
  console.log(`📧 Email service configured`);
  console.log(`👥 Sample users: <EMAIL> / <EMAIL> (password: password123)`);
});
