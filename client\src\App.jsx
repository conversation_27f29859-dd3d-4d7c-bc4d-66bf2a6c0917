import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { ThemeProvider } from '@/contexts/ThemeContext';
import { AuthProvider } from '@/contexts/AuthContext';
import { CartProvider } from '@/contexts/CartContext';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import HomePage from '@/pages/HomePage';
import ProductsPage from '@/pages/ProductsPage';
import ProductDetailPage from '@/pages/ProductDetailPage';
import CartPage from '@/pages/CartPage';
import CheckoutPage from '@/pages/CheckoutPage';
import OrdersPage from '@/pages/OrdersPage';
import LoginPage from '@/pages/auth/LoginPage';
import RegisterPage from '@/pages/auth/RegisterPage';
import SellerDashboard from '@/pages/seller/SellerDashboard';
import AddProductPage from '@/pages/seller/AddProductPage';
import ManageOrdersPage from '@/pages/seller/ManageOrdersPage';
import CouponsPage from '@/pages/seller/CouponsPage';
import ProtectedRoute from '@/components/ProtectedRoute';
import { ToastProvider, ToastViewport } from '@/components/ui/toast';
import axios from 'axios';

// Set axios base URL
axios.defaults.baseURL = 'http://localhost:5000';

function App() {
  return (
    <ThemeProvider>
      <AuthProvider>
        <CartProvider>
          <ToastProvider>
            <Router>
              <div className="min-h-screen flex flex-col">
                <Header />
                <main className="flex-1">
                  <Routes>
                    {/* Public Routes */}
                    <Route path="/" element={<HomePage />} />
                    <Route path="/products" element={<ProductsPage />} />
                    <Route path="/products/:id" element={<ProductDetailPage />} />
                    <Route path="/login" element={<LoginPage />} />
                    <Route path="/register" element={<RegisterPage />} />

                    {/* Customer Protected Routes */}
                    <Route 
                      path="/cart" 
                      element={
                        <ProtectedRoute requiredRole="customer">
                          <CartPage />
                        </ProtectedRoute>
                      } 
                    />
                    <Route 
                      path="/checkout" 
                      element={
                        <ProtectedRoute requiredRole="customer">
                          <CheckoutPage />
                        </ProtectedRoute>
                      } 
                    />
                    <Route 
                      path="/orders" 
                      element={
                        <ProtectedRoute requiredRole="customer">
                          <OrdersPage />
                        </ProtectedRoute>
                      } 
                    />

                    {/* Seller Protected Routes */}
                    <Route 
                      path="/seller/dashboard" 
                      element={
                        <ProtectedRoute requiredRole="seller">
                          <SellerDashboard />
                        </ProtectedRoute>
                      } 
                    />
                    <Route 
                      path="/seller/add-product" 
                      element={
                        <ProtectedRoute requiredRole="seller">
                          <AddProductPage />
                        </ProtectedRoute>
                      } 
                    />
                    <Route 
                      path="/seller/orders" 
                      element={
                        <ProtectedRoute requiredRole="seller">
                          <ManageOrdersPage />
                        </ProtectedRoute>
                      } 
                    />
                    <Route 
                      path="/seller/coupons" 
                      element={
                        <ProtectedRoute requiredRole="seller">
                          <CouponsPage />
                        </ProtectedRoute>
                      } 
                    />
                  </Routes>
                </main>
                <Footer />
              </div>
              <ToastViewport />
            </Router>
          </ToastProvider>
        </CartProvider>
      </AuthProvider>
    </ThemeProvider>
  );
}

export default App;
